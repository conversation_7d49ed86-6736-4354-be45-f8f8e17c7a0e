#!/bin/bash

# API缓存功能测试脚本
# 测试帖子详情和列表接口的缓存功能

# 配置
API_BASE_URL="http://localhost:8080"  # 根据实际端口调整
CONTENT_TYPE="Content-Type: application/json"

echo "=== API缓存功能测试 ==="

# 检查服务是否运行
echo "检查服务状态..."
curl -s --connect-timeout 5 "$API_BASE_URL/health" > /dev/null
if [ $? -ne 0 ]; then
    echo "❌ 服务未运行或无法连接到 $API_BASE_URL"
    echo "请确保应用服务正在运行"
    exit 1
fi

echo "✅ 服务连接正常"

# 测试1：帖子详情缓存测试
echo -e "\n--- 测试1：帖子详情缓存 ---"

# 测试存在的帖子
EXISTING_POST_ID="test-post-123"  # 需要根据实际数据调整

echo "测试存在的帖子详情缓存..."

# 第一次请求（缓存未命中）
echo "第一次请求（应该查询数据库）..."
start_time=$(date +%s%N)
response1=$(curl -s -w "%{http_code}" -H "$CONTENT_TYPE" \
    "$API_BASE_URL/api/v1/post/detail?id=$EXISTING_POST_ID")
end_time=$(date +%s%N)
duration1=$(( (end_time - start_time) / 1000000 ))

http_code1=${response1: -3}
response_body1=${response1%???}

echo "第一次请求耗时: ${duration1}ms, HTTP状态码: $http_code1"

# 第二次请求（缓存命中）
echo "第二次请求（应该命中缓存）..."
start_time=$(date +%s%N)
response2=$(curl -s -w "%{http_code}" -H "$CONTENT_TYPE" \
    "$API_BASE_URL/api/v1/post/detail?id=$EXISTING_POST_ID")
end_time=$(date +%s%N)
duration2=$(( (end_time - start_time) / 1000000 ))

http_code2=${response2: -3}
response_body2=${response2%???}

echo "第二次请求耗时: ${duration2}ms, HTTP状态码: $http_code2"

# 比较响应时间
if [ $duration2 -lt $duration1 ]; then
    echo "✅ 缓存命中：第二次请求更快 (${duration1}ms -> ${duration2}ms)"
else
    echo "⚠️  缓存可能未命中：第二次请求未明显加速"
fi

# 测试不存在的帖子（空值缓存）
echo -e "\n测试不存在的帖子（空值缓存）..."
NON_EXISTENT_POST_ID="non-existent-$(date +%s)"

# 第一次请求不存在的帖子
echo "第一次请求不存在的帖子..."
start_time=$(date +%s%N)
response3=$(curl -s -w "%{http_code}" -H "$CONTENT_TYPE" \
    "$API_BASE_URL/api/v1/post/detail?id=$NON_EXISTENT_POST_ID")
end_time=$(date +%s%N)
duration3=$(( (end_time - start_time) / 1000000 ))

http_code3=${response3: -3}
echo "第一次请求不存在帖子耗时: ${duration3}ms, HTTP状态码: $http_code3"

# 第二次请求同一个不存在的帖子（应该命中空值缓存）
echo "第二次请求同一个不存在的帖子..."
start_time=$(date +%s%N)
response4=$(curl -s -w "%{http_code}" -H "$CONTENT_TYPE" \
    "$API_BASE_URL/api/v1/post/detail?id=$NON_EXISTENT_POST_ID")
end_time=$(date +%s%N)
duration4=$(( (end_time - start_time) / 1000000 ))

http_code4=${response4: -3}
echo "第二次请求不存在帖子耗时: ${duration4}ms, HTTP状态码: $http_code4"

if [ $duration4 -lt $duration3 ]; then
    echo "✅ 空值缓存生效：第二次请求更快 (${duration3}ms -> ${duration4}ms)"
else
    echo "⚠️  空值缓存可能未生效"
fi

# 测试2：帖子列表缓存测试
echo -e "\n--- 测试2：帖子列表缓存 ---"

echo "测试帖子列表缓存..."

# 第一次请求列表
echo "第一次请求列表（应该查询数据库）..."
start_time=$(date +%s%N)
list_response1=$(curl -s -w "%{http_code}" -H "$CONTENT_TYPE" \
    "$API_BASE_URL/api/v1/post/list?page=1&size=10")
end_time=$(date +%s%N)
list_duration1=$(( (end_time - start_time) / 1000000 ))

list_http_code1=${list_response1: -3}
echo "第一次列表请求耗时: ${list_duration1}ms, HTTP状态码: $list_http_code1"

# 第二次请求列表
echo "第二次请求列表（应该命中缓存）..."
start_time=$(date +%s%N)
list_response2=$(curl -s -w "%{http_code}" -H "$CONTENT_TYPE" \
    "$API_BASE_URL/api/v1/post/list?page=1&size=10")
end_time=$(date +%s%N)
list_duration2=$(( (end_time - start_time) / 1000000 ))

list_http_code2=${list_response2: -3}
echo "第二次列表请求耗时: ${list_duration2}ms, HTTP状态码: $list_http_code2"

if [ $list_duration2 -lt $list_duration1 ]; then
    echo "✅ 列表缓存命中：第二次请求更快 (${list_duration1}ms -> ${list_duration2}ms)"
else
    echo "⚠️  列表缓存可能未命中"
fi

# 测试3：并发请求测试
echo -e "\n--- 测试3：并发请求测试 ---"

echo "测试并发请求同一个帖子详情..."

# 并发请求数量
CONCURRENT_REQUESTS=10

# 创建临时文件存储结果
temp_file="/tmp/concurrent_test_$$"

# 并发请求
for i in $(seq 1 $CONCURRENT_REQUESTS); do
    (
        start_time=$(date +%s%N)
        response=$(curl -s -w "%{http_code}" -H "$CONTENT_TYPE" \
            "$API_BASE_URL/api/v1/post/detail?id=$EXISTING_POST_ID")
        end_time=$(date +%s%N)
        duration=$(( (end_time - start_time) / 1000000 ))
        http_code=${response: -3}
        echo "$i:$duration:$http_code" >> "$temp_file"
    ) &
done

# 等待所有请求完成
wait

# 分析结果
if [ -f "$temp_file" ]; then
    echo "并发请求结果："
    total_duration=0
    success_count=0
    
    while IFS=':' read -r req_id duration http_code; do
        echo "请求 $req_id: ${duration}ms, HTTP状态码: $http_code"
        if [ "$http_code" = "200" ]; then
            total_duration=$((total_duration + duration))
            success_count=$((success_count + 1))
        fi
    done < "$temp_file"
    
    if [ $success_count -gt 0 ]; then
        avg_duration=$((total_duration / success_count))
        echo "✅ 并发测试完成：$success_count/$CONCURRENT_REQUESTS 成功，平均耗时: ${avg_duration}ms"
    else
        echo "❌ 并发测试失败：所有请求都失败了"
    fi
    
    # 清理临时文件
    rm -f "$temp_file"
else
    echo "❌ 并发测试失败：无法创建临时文件"
fi

# 测试4：缓存TTL验证
echo -e "\n--- 测试4：缓存TTL验证 ---"

echo "检查Redis中的缓存TTL..."

# 检查帖子详情缓存TTL
detail_key="post:detail:$EXISTING_POST_ID"
detail_ttl=$(redis-cli ttl "$detail_key" 2>/dev/null)
if [ $? -eq 0 ] && [ "$detail_ttl" != "-2" ]; then
    echo "帖子详情缓存TTL: ${detail_ttl}秒"
    if [ $detail_ttl -gt 540 ] && [ $detail_ttl -le 660 ]; then  # 9-11分钟
        echo "✅ 详情缓存TTL在预期范围内 (9-11分钟)"
    else
        echo "⚠️  详情缓存TTL超出预期范围"
    fi
else
    echo "⚠️  无法获取详情缓存TTL或缓存不存在"
fi

# 检查帖子列表缓存TTL
list_key="post:list:1:10"
list_ttl=$(redis-cli ttl "$list_key" 2>/dev/null)
if [ $? -eq 0 ] && [ "$list_ttl" != "-2" ]; then
    echo "帖子列表缓存TTL: ${list_ttl}秒"
    if [ $list_ttl -gt 540 ] && [ $list_ttl -le 660 ]; then  # 9-11分钟
        echo "✅ 列表缓存TTL在预期范围内 (9-11分钟)"
    else
        echo "⚠️  列表缓存TTL超出预期范围"
    fi
else
    echo "⚠️  无法获取列表缓存TTL或缓存不存在"
fi

echo -e "\n=== API缓存测试完成 ==="
echo "测试总结："
echo "1. 帖子详情缓存：正常缓存 + 空值缓存防穿透"
echo "2. 帖子列表缓存：正常缓存，不使用空值缓存"
echo "3. 随机TTL：防止缓存雪崩"
echo "4. 并发处理：验证高并发下的缓存表现"
