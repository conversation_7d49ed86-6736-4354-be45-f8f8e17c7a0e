package global

import (
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	"e.coding.net/g-dtay0385/common/go-config"
	"e.coding.net/g-dtay0385/common/go-initialize"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-mysql-gorm"
	"e.coding.net/g-dtay0385/common/go-redis"
	tracerProvider "e.coding.net/g-dtay0385/common/opentelemetry-tracer-provider"
)

// GlobalConfig 全局配置
var GlobalConfig = &Config{}

// Service 服务配置
type Service struct {
	Name    string `json:"name"`
	Version string `json:"version"`
	Address string `json:"address"`
	Token   string `json:"token"` // 调用令牌
	Env     string `json:"env"`
}

// Config 全局配置
type Config struct {
	NaCos      *config.NaCosConfig        `json:"nacos"`
	Logger     *log.Conf                  `json:"logger"`
	Mysql      map[string]*mysql.Config   `json:"mysql"`
	Redis      map[string]*redis.Config   `json:"redis"`
	Kafka      []*initialize.BrokerConfig `json:"kafka"`
	Mongo      *MongoConf                 `json:"mongo"`
	Service    *Service                   `json:"service"`
	MasterHttp *request.Config            `json:"master_http"` // 元气服务配置
	WarnId     string                     `json:"warn_id"`
	Yc         *YcConf                    `json:"yc"`
	Pbs        *PbsConf                   `json:"pbs"`
	JPush      *JPushConf                 `json:"jpush"`
	Tracer     *tracerProvider.Config     `json:"tracer"`
	Auth       *AuthConf                  `json:"auth"`
}

type MongoConf struct {
	Uri      string `json:"uri"`      //地址
	User     string `json:"user"`     //用户名
	Password string `json:"password"` //密码
	Database string `json:"database"` //验证数据库
	TimeOut  int    `json:"timeout"`  //连接超时时间
	MaxNum   int    `json:"maxnum"`   //最大连接数
}

type YcConf struct {
	ClientId     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	Host         string `json:"host"`
}

type PbsConf struct {
	AdminHost string `json:"admin_host"`
}

type JPushConf struct {
	AppKey       string `json:"app_key"`
	MasterSecret string `json:"master_secret"`
}

type AuthConf struct {
	RemoteTimeout  int    `json:"remote_timeout"`  // 远程API超时时间（秒）
	PublicKey      string `json:"public_key"`      // JWT公钥用于本地验证
	EnableFallback bool   `json:"enable_fallback"` // 是否启用本地验证降级
}
