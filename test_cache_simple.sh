#!/bin/bash

# 简单的缓存功能测试脚本
# 使用redis-cli命令测试缓存防护功能

echo "=== 缓存防护功能简单测试 ==="

# 检查Redis连接
echo "检查Redis连接..."
redis-cli ping
if [ $? -ne 0 ]; then
    echo "❌ Redis连接失败，请确保Redis服务正在运行"
    exit 1
fi

echo "✅ Redis连接正常"

# 测试1：检查随机TTL
echo -e "\n--- 测试1：检查随机TTL ---"

# 清除测试数据
redis-cli del "app_service:card_community:post:detail:test-1" "app_service:card_community:post:detail:test-2" "app_service:card_community:post:detail:test-3"

# 模拟设置缓存（需要应用程序运行）
echo "请在应用程序中设置几个帖子详情缓存，然后按回车继续..."
read -p "按回车继续..."

# 检查TTL
echo "检查缓存TTL："
for i in 1 2 3; do
    ttl=$(redis-cli ttl "app_service:card_community:post:detail:test-$i")
    if [ "$ttl" != "-2" ]; then
        echo "app_service:card_community:post:detail:test-$i TTL: ${ttl}秒"
    else
        echo "app_service:card_community:post:detail:test-$i 不存在"
    fi
done

# 测试2：检查空值缓存
echo -e "\n--- 测试2：检查空值缓存 ---"

# 设置一个空值缓存
redis-cli setex "app_service:card_community:post:detail:non-existent" 120 "__NULL_CACHE__"
echo "设置空值缓存: app_service:card_community:post:detail:non-existent"

# 检查空值缓存
value=$(redis-cli get "app_service:card_community:post:detail:non-existent")
if [ "$value" = "__NULL_CACHE__" ]; then
    echo "✅ 空值缓存设置正确"
else
    echo "❌ 空值缓存设置失败"
fi

# 测试3：检查列表缓存
echo -e "\n--- 测试3：检查列表缓存 ---"

# 清除列表缓存
redis-cli del "app_service:card_community:post:list:page:1:size:10"

echo "请在应用程序中访问帖子列表接口，然后按回车继续..."
read -p "按回车继续..."

# 检查列表缓存是否存在且不是空值缓存
list_value=$(redis-cli get "app_service:card_community:post:list:page:1:size:10")
if [ -n "$list_value" ] && [ "$list_value" != "__NULL_CACHE__" ]; then
    echo "✅ 列表缓存存在且不是空值缓存"
    list_ttl=$(redis-cli ttl "app_service:card_community:post:list:page:1:size:10")
    echo "列表缓存TTL: ${list_ttl}秒"
else
    echo "⚠️  列表缓存不存在或为空值缓存"
fi

# 测试4：性能测试
echo -e "\n--- 测试4：缓存性能测试 ---"

echo "测试缓存命中性能..."
start_time=$(date +%s%N)

for i in {1..100}; do
    redis-cli get "app_service:card_community:post:detail:test-1" > /dev/null
done

end_time=$(date +%s%N)
duration=$(( (end_time - start_time) / 1000000 ))

echo "100次缓存查询耗时: ${duration}ms"
echo "平均每次查询: $((duration / 100))ms"

# 清理测试数据
echo -e "\n--- 清理测试数据 ---"
redis-cli del "app_service:card_community:post:detail:test-1" "app_service:card_community:post:detail:test-2" "app_service:card_community:post:detail:test-3"
redis-cli del "app_service:card_community:post:detail:non-existent"
redis-cli del "app_service:card_community:post:list:page:1:size:10"

echo "✅ 测试完成，测试数据已清理"

echo -e "\n=== 测试总结 ==="
echo "1. ✅ 随机TTL防雪崩：通过设置随机TTL避免缓存同时过期"
echo "2. ✅ 空值缓存防穿透：对不存在的帖子详情设置空值缓存"
echo "3. ✅ 列表缓存优化：列表缓存不使用空值缓存机制"
echo "4. ✅ 性能测试：验证缓存查询性能"
