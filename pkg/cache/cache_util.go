package cache

import (
	"context"
	"encoding/json"
	"errors"
	"math/rand"
	"time"

	"app_service/global"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/go-redis/redis/v8"
)

const (
	// NullCacheValue 空值缓存标记
	NullCacheValue = "__NULL_CACHE__"
	// NullCacheTTL 空值缓存过期时间（较短，避免长时间缓存错误的空值）
	NullCacheTTL = 2 * time.Minute
)

// CacheOptions 缓存选项
type CacheOptions struct {
	// BaseTTL 基础过期时间
	BaseTTL time.Duration
	// RandomFactor 随机因子，范围0-1，实际TTL = BaseTTL + random(0, BaseTTL * RandomFactor)
	RandomFactor float64
	// EnableNullCache 是否启用空值缓存
	EnableNullCache bool
}

// DefaultCacheOptions 默认缓存选项
func DefaultCacheOptions(baseTTL time.Duration) *CacheOptions {
	return &CacheOptions{
		BaseTTL:         baseTTL,
		RandomFactor:    0.1, // 10%的随机时间
		EnableNullCache: true,
	}
}

// GetRandomTTL 获取随机TTL，防止缓存雪崩
func (opts *CacheOptions) GetRandomTTL() time.Duration {
	if opts.RandomFactor <= 0 {
		return opts.BaseTTL
	}
	
	// 计算随机时间：0 到 BaseTTL * RandomFactor
	randomDuration := time.Duration(float64(opts.BaseTTL) * opts.RandomFactor * rand.Float64())
	return opts.BaseTTL + randomDuration
}

// SetCache 设置缓存，支持随机TTL
func SetCache(ctx context.Context, key string, value interface{}, opts *CacheOptions) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		log.Ctx(ctx).Errorf("序列化缓存数据失败 key:%s, err:%v", key, err)
		return err
	}

	ttl := opts.GetRandomTTL()
	if err := global.REDIS.Set(ctx, key, jsonData, ttl).Err(); err != nil {
		log.Ctx(ctx).Errorf("设置缓存失败 key:%s, ttl:%v, err:%v", key, ttl, err)
		return err
	}

	return nil
}

// GetCache 获取缓存，支持空值缓存检测
func GetCache(ctx context.Context, key string, result interface{}) (bool, error) {
	cachedData, err := global.REDIS.Get(ctx, key).Result()
	
	switch {
	case err == nil:
		// 检查是否为空值缓存
		if cachedData == NullCacheValue {
			return false, nil // 返回false表示数据不存在（但缓存命中了空值）
		}
		
		// 正常缓存，反序列化数据
		if err := json.Unmarshal([]byte(cachedData), result); err != nil {
			log.Ctx(ctx).Errorf("解析缓存数据失败 key:%s, err:%v", key, err)
			return false, err
		}
		return true, nil

	case errors.Is(err, redis.Nil):
		// 缓存未命中
		return false, nil

	default:
		log.Ctx(ctx).Errorf("获取缓存失败 key:%s, err:%v", key, err)
		return false, err
	}
}

// SetNullCache 设置空值缓存，防止缓存穿透
func SetNullCache(ctx context.Context, key string) error {
	if err := global.REDIS.Set(ctx, key, NullCacheValue, NullCacheTTL).Err(); err != nil {
		log.Ctx(ctx).Errorf("设置空值缓存失败 key:%s, err:%v", key, err)
		return err
	}
	return nil
}

// IsNullCache 检查是否为空值缓存
func IsNullCache(value string) bool {
	return value == NullCacheValue
}

// CacheResult 缓存结果结构
type CacheResult struct {
	Found bool        // 是否找到缓存
	IsNull bool       // 是否为空值缓存
	Data  interface{} // 缓存数据
}

// GetCacheWithResult 获取缓存并返回详细结果
func GetCacheWithResult(ctx context.Context, key string, result interface{}) (*CacheResult, error) {
	cachedData, err := global.REDIS.Get(ctx, key).Result()
	
	switch {
	case err == nil:
		// 检查是否为空值缓存
		if cachedData == NullCacheValue {
			return &CacheResult{
				Found:  true,
				IsNull: true,
				Data:   nil,
			}, nil
		}
		
		// 正常缓存，反序列化数据
		if err := json.Unmarshal([]byte(cachedData), result); err != nil {
			log.Ctx(ctx).Errorf("解析缓存数据失败 key:%s, err:%v", key, err)
			return &CacheResult{Found: false}, err
		}
		
		return &CacheResult{
			Found:  true,
			IsNull: false,
			Data:   result,
		}, nil

	case errors.Is(err, redis.Nil):
		// 缓存未命中
		return &CacheResult{Found: false}, nil

	default:
		log.Ctx(ctx).Errorf("获取缓存失败 key:%s, err:%v", key, err)
		return &CacheResult{Found: false}, err
	}
}
