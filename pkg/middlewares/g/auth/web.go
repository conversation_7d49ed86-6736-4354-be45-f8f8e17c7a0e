package auth

import (
	"context"
	"time"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request/pat"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type Web struct {
	// 不需要鉴权的地址
	NoAuthUrl []string
	// 远程API超时时间（秒）
	RemoteTimeout int
}

func (c *Web) CheckToken(token string, ctx context.Context) (any, error) {
	// 设置超时时间，默认10秒
	timeout := time.Duration(c.RemoteTimeout) * time.Second
	if timeout == 0 {
		timeout = 10 * time.Second
	}

	// 创建带超时的上下文
	remoteCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	log.Ctx(ctx).Infof("开始远程JWT验证，超时时间: %v", timeout)

	// 远程校验token 获取用户信息
	info, err := pat.CheckUserJwt(remoteCtx, token)
	if err != nil {
		log.Ctx(ctx).Errorf("远程JWT验证失败: %v", err)
		return nil, err
	}

	if info == nil {
		log.Ctx(ctx).Error("远程JWT验证返回空用户信息")
		return nil, response.TokenErr.SetMsg("鉴权失败")
	}

	log.Ctx(ctx).Info("远程JWT验证成功")
	return info, nil
}

func (c *Web) GetNoAuthUrl() []string {
	return c.NoAuthUrl
}

// GetUserFromCtx 获取用户信息
func GetUserFromCtx(ctx context.Context) (*pat.CheckUserJwtUserInfo, bool) {
	acc, ok := ctx.Value(userInfo).(*pat.CheckUserJwtUserInfo)
	return acc, ok
}
