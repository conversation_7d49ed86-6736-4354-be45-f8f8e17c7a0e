package facade

import (
	common_define "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model"
	"app_service/apps/platform/user/dal/model/mongdb"
	"app_service/apps/platform/user/repo"
	"app_service/global"
	"app_service/pkg/search"
	"context"
	"errors"

	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/v2/bson"
	"gorm.io/gorm"
)

func GetNewUser(ctx context.Context, userId string) (*model.NewUser, error) {
	newUserSchema := repo.GetQuery().NewUser
	queryWrapper := search.NewQueryBuilder().Eq(newUserSchema.UserID, userId).Build()
	newUserTab, err := repo.NewNewUserRepo(newUserSchema.WithContext(ctx)).SelectOne(queryWrapper)
	if err != nil {
		return nil, common_define.CommonErr.Err(err)
	}
	return newUserTab, nil
}

func GetUserIdList(ctx context.Context, keyword string) ([]string, error) {
	newUserSchema := repo.GetQuery().NewUser
	queryWrapper := search.NewQueryBuilder().
		Or().
		Like(newUserSchema.Nickname, "%"+keyword+"%").
		Eq(newUserSchema.UserID, keyword).
		Eq(newUserSchema.MobilePhone, keyword).
		Done().Build()
	newUserTabList, err := repo.NewNewUserRepo(newUserSchema.WithContext(ctx)).SelectList(queryWrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, common_define.CommonErr.Err(err)
	}

	userIdList := make([]string, 0)
	for _, newUser := range newUserTabList {
		userIdList = append(userIdList, newUser.UserID)
	}
	return userIdList, nil
}

func GetUserLoginLog(ctx context.Context, userId string, recent int32) ([]*model.UserLoginLog, error) {
	loginLogSchema := repo.GetQuery().UserLoginLog
	queryWrapper := search.NewQueryBuilder().
		Eq(loginLogSchema.UserID, userId).
		OrderByDesc(loginLogSchema.LoginTime).Build()
	userLoginLogList, _, err := repo.NewUserLoginLogRepo(loginLogSchema.WithContext(ctx)).SelectPage(queryWrapper, 1, int(recent))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, common_define.CommonErr.Err(err)
	}
	return userLoginLogList, nil
}

func GetNodeUser(ctx context.Context, userId string) (*mongdb.User, error) {
	userCollection := mongox.NewCollection[mongdb.User](global.Tmt(), "users")
	userObjectId, err := bson.ObjectIDFromHex(userId)
	if err != nil {
		return nil, err
	}
	qw := query.NewBuilder().Id(userObjectId).Build()
	user, err := userCollection.Finder().Filter(qw).FindOne(ctx)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func GetNodeUserByOpenUserId(ctx context.Context, openUserId string) (*mongdb.User, error) {
	userCollection := mongox.NewCollection[mongdb.User](global.Tmt(), "users")
	userObjectId, err := bson.ObjectIDFromHex(openUserId)
	if err != nil {
		return nil, err
	}
	qw := query.NewBuilder().Eq("open_info.open_user_id", userObjectId).Build()
	user, err := userCollection.Finder().Filter(qw).FindOne(ctx)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func GetNodeUserByOpenUserIdMap(ctx context.Context, openUserIds []string) (map[string]*mongdb.User, error) {
	userCollection := mongox.NewCollection[mongdb.User](global.Tmt(), "users")
	var userObjectIds []interface{}
	for _, openUserId := range openUserIds {
		userObjectId, err := bson.ObjectIDFromHex(openUserId)
		if err != nil {
			return nil, err
		}
		userObjectIds = append(userObjectIds, userObjectId)
	}
	qw := query.NewBuilder().In("open_info.open_user_id", userObjectIds...).Build()
	userList, err := userCollection.Finder().Filter(qw).Find(ctx)
	if err != nil {
		return nil, err
	}
	userMap := make(map[string]*mongdb.User, len(userList))
	for _, user := range userList {
		userMap[user.OpenInfo.OpenUserID.Hex()] = user
	}
	return userMap, nil
}

func GetOpenUserId(ctx context.Context, userId string) (string, error) {
	user, err := GetNodeUser(ctx, userId)
	if err != nil {
		return "", err
	}
	if user != nil && !user.OpenInfo.OpenUserID.IsZero() {
		return user.OpenInfo.OpenUserID.Hex(), nil
	}
	return "", nil
}

func GetNodeUsers(ctx context.Context, userIds []string) ([]*mongdb.User, error) {
	if len(userIds) == 0 {
		return nil, nil
	}
	userCollection := mongox.NewCollection[mongdb.User](global.Tmt(), "users")
	var userObjectIds []interface{}
	for _, userId := range userIds {
		userObjectId, err := bson.ObjectIDFromHex(userId)
		if err != nil {
			return nil, err
		}
		userObjectIds = append(userObjectIds, userObjectId)
	}

	qw := query.NewBuilder().In("_id", userObjectIds...).Build()
	userList, err := userCollection.Finder().Filter(qw).Find(ctx)
	if err != nil {
		return nil, err
	}
	return userList, nil
}

func GetNodeUserMap(ctx context.Context, userIds []string) (map[string]*mongdb.User, error) {
	userList, err := GetNodeUsers(ctx, userIds)
	if err != nil {
		return nil, err
	}
	userMap := make(map[string]*mongdb.User, len(userList))
	for _, user := range userList {
		userMap[user.ID.Hex()] = user
	}
	return userMap, nil
}
